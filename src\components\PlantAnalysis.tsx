import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  IconButton,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CameraAlt as CameraIcon,
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';
import geminiService from '../services/geminiService';
import translations from '../translations';
import PlantAnalysisPopup from './PlantAnalysisPopup';

const PlantAnalysis = () => {
  const { language } = useLanguage();
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [showPopup, setShowPopup] = useState(false);


  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      validateAndProcessImage(file);
    }
  };

  const validateAndProcessImage = (file: File) => {
    setError('');
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      setError(translations[language as keyof typeof translations].plantAnalysis.invalidFileType);
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      setError(translations[language as keyof typeof translations].plantAnalysis.fileTooLarge);
      return;
    }
    setSelectedImage(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };





  const analyzePlant = async () => {
    if (!selectedImage) return;
    setIsAnalyzing(true);
    setError('');
    setAnalysisResult('');
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const base64Image = e.target?.result as string;
        const prompt = language === 'en'
          ? `Analyze this plant leaf image for diseases, pests, or nutrient deficiencies.`
          : language === 'kn'
          ? `ಈ ಸಸ್ಯದ ಎಲೆಯ ಚಿತ್ರವನ್ನು ರೋಗಗಳು, ಕೀಟಗಳು ಅಥವಾ ಪೌಷ್ಟಿಕಾಂಶ ಕೊರತೆಗಳಿಗಾಗಿ ವಿಶ್ಲೇಷಿಸಿ.`
          : `इस पौधे के पत्ते की छवि का विश्लेषण रोगों, कीटों या पोषक तत्वों की कमी के लिए करें।`;
        const result = await geminiService.analyzeImage(base64Image, prompt);
        setAnalysisResult(result);
        setShowPopup(true); // Show popup with results
      } catch (err) {
        setError(translations[language as keyof typeof translations].plantAnalysis.analysisError);
      } finally {
        setIsAnalyzing(false);
      }
    };
    reader.onerror = () => {
      setError('Failed to read the image file.');
      setIsAnalyzing(false);
    };
    reader.readAsDataURL(selectedImage);
  };

  const resetAnalysis = () => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setAnalysisResult('');
    setError('');
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
  };

  const t = translations[language as keyof typeof translations].plantAnalysis;



  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: { xs: 2, sm: 3 } }}>
      <Grid container spacing={4}>
        {/* Left Column */}
        <Grid item xs={12} md={5}>
            <Card sx={{ height: '100%', bgcolor: 'rgba(255, 255, 255, 0.9)', backdropFilter: 'blur(10px)' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    <Typography variant="h5" gutterBottom sx={{ color: '#1B4C35', fontWeight: 600 }}>{t.title}</Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>{t.description}</Typography>
                    <Box sx={{ mb: 3 }}>
                        <Typography variant="h6" sx={{ color: '#2E7D32', fontWeight: 600, mb: 2 }}>{t.infoTitle}</Typography>
                        <Grid container spacing={1}>
                            {[t.infoPoint1, t.infoPoint2, t.infoPoint3, t.infoPoint4].map((point, index) => (
                                <Grid item xs={12} key={index}>
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                        <CheckCircleIcon sx={{ color: '#4caf50', mr: 1.5, mt: 0.25, flexShrink: 0 }} />
                                        <Typography variant="body2" color="text.secondary">{point}</Typography>
                                    </Box>
                                </Grid>
                            ))}
                        </Grid>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <Button variant="outlined" startIcon={<UploadIcon />} component="label" sx={{ borderColor: '#4caf50', color: '#4caf50', fontWeight: 600, textTransform: 'none', '&:hover': { borderColor: '#43a047', bgcolor: 'rgba(76, 175, 80, 0.05)' } }}>
                            {t.browseFiles}
                            <input type="file" hidden accept="image/*" onChange={handleImageChange} />
                        </Button>

                        {/* Camera Button - Works on both desktop and mobile */}
                        <Button
                            variant="outlined"
                            startIcon={<CameraIcon />}
                            component="label"
                            sx={{
                                borderColor: '#2196F3',
                                color: '#2196F3',
                                fontWeight: 600,
                                textTransform: 'none',
                                '&:hover': {
                                    borderColor: '#1976D2',
                                    bgcolor: 'rgba(33, 150, 243, 0.05)'
                                }
                            }}
                        >
                            Take Photo
                            <input
                                type="file"
                                hidden
                                accept="image/*"
                                capture="environment"
                                onChange={handleImageChange}
                            />
                        </Button>
                    </Box>
                </CardContent>
            </Card>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} md={7}>
            <Card sx={{ height: '100%', bgcolor: 'rgba(255, 255, 255, 0.9)', backdropFilter: 'blur(10px)' }}>
                <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
                    {!selectedImage ? (
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 4, border: 2, borderStyle: 'dashed', borderColor: 'rgba(76, 175, 80, 0.3)', borderRadius: 2, bgcolor: 'rgba(76, 175, 80, 0.05)' }}>
                            <UploadIcon sx={{ fontSize: 48, color: '#4caf50', mb: 2 }} />
                            <Typography variant="h6" gutterBottom sx={{ color: '#1B4C35', fontWeight: 600 }}>{t.uploadTitle}</Typography>
                            <Typography variant="body2" color="text.secondary" textAlign="center">{t.uploadDescription}</Typography>
                        </Box>
                    ) : (
                        <Box>
                            <Box sx={{ position: 'relative', mb: 3 }}>
                                <Box component="img" src={previewUrl || ''} alt="Plant preview" sx={{ width: '100%', height: '300px', objectFit: 'cover', borderRadius: 2, border: 1, borderColor: 'divider' }} />
                                <IconButton onClick={resetAnalysis} sx={{ position: 'absolute', top: 8, right: 8, bgcolor: 'rgba(0, 0, 0, 0.5)', color: 'white', '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' } }}>
                                    <CloseIcon />
                                </IconButton>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 2 }}>
                                <Button variant="contained" onClick={analyzePlant} disabled={isAnalyzing} sx={{ bgcolor: '#4caf50', color: 'white', fontWeight: 600, textTransform: 'none', minWidth: 150, minHeight: 40, '&:hover': { bgcolor: '#43a047' } }}>
                                    {isAnalyzing ? t.analyzing : t.analyze}
                                </Button>
                                <Button variant="outlined" onClick={resetAnalysis} disabled={isAnalyzing} sx={{ borderColor: '#f44336', color: '#f44336', fontWeight: 600, textTransform: 'none', '&:hover': { borderColor: '#d32f2f', bgcolor: 'rgba(244, 67, 54, 0.05)' } }}>
                                    {t.reset}
                                </Button>
                            </Box>
                        </Box>
                    )}
                    {isAnalyzing && <LinearProgress color="success" sx={{ my: 2 }} />}
                    {error && (<Alert severity="error" icon={<ErrorIcon />} sx={{ mt: 2 }}>{error}</Alert>)}
                    {analysisResult && (
                        <Box sx={{ mt: 2 }}>
                            <Alert severity="success" icon={<CheckCircleIcon />} sx={{ mb: 2 }}>
                                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                                    {t.result} - Analysis Complete!
                                </Typography>
                            </Alert>
                            <Button
                                variant="contained"
                                fullWidth
                                onClick={() => setShowPopup(true)}
                                startIcon={<ViewIcon />}
                                sx={{
                                    bgcolor: '#4caf50',
                                    color: 'white',
                                    fontWeight: 600,
                                    textTransform: 'none',
                                    py: 1.5,
                                    '&:hover': { bgcolor: '#43a047' }
                                }}
                            >
                                {language === 'kn' ? 'ವಿಶ್ಲೇಷಣೆ ವೀಕ್ಷಿಸಿ' :
                                 language === 'hi' ? 'विश्लेषण देखें' :
                                 'View Detailed Analysis'}
                            </Button>
                        </Box>
                    )}
                </CardContent>
            </Card>
        </Grid>
      </Grid>

      {/* Plant Analysis Popup */}
      <PlantAnalysisPopup
        open={showPopup}
        onClose={() => setShowPopup(false)}
        analysisResult={analysisResult}
        language={language}
      />
    </Box>
  );
};

export default PlantAnalysis;